# Cloud ML v2

CloudML : Package, person and animal detection on MP4 file

Epic : https://jira.mios.com/browse/ENMC-9016

## Changelog
- v3.1
  - Fix: make /jobs/count/queued O(1) and avoid queue scans (https://jira.mios.com/browse/ENMC-11640)
- v2.9
  - Standalone Redis to Sentinel Redis (https://jira.mios.com/browse/ENMC-11417)
- v2.8
  - Finding the difference between the Start and End Frames (https://jira.mios.com/browse/ENMC-11375)
- v2.7
  - Ezlo Yolo 11 model performance improvement (https://jira.mios.com/browse/ENMC-11317)
- v2.6
  - Sending Skip Frame Factor and ML model values with payload (https://jira.mios.com/browse/ENMC-11249)
- v2.5
  - Generating endpoints related to the number of jobs processed (https://jira.mios.com/browse/ENMC-11221)
- v2.4
  - Defining the number of workers with .env file (https://jira.mios.com/browse/ENMC-11186)
- v2.3
  - <PERSON><PERSON><PERSON> model change and CPU architecture (https://jira.mios.com/browse/ENMC-11181)
- v2.2
  - OpenTelemetry implementation (https://jira.mios.com/browse/EFWR-5385)
- v2.1
  - Send errors CloudML gets back to the Cloud (https://jira.mios.com/browse/ENMC-11118)
- v2.0
  - requests with the same jobid will not be processed (https://jira.mios.com/browse/EFWR-5374)
- v1.9
  - face recognition, barcode and person detection has been disabled (https://jira.mios.com/browse/EFWR-5365)
- v1.8.1
  - CloudML performance improvements (https://jira.mios.com/browse/EFWR-5354)
- v1.8
  - vehicle bicycle = disable (https://jira.mios.com/browse/EFWR-5353)
- v1.7
  - OpenTelemetry Attributes (https://jira.mios.com/browse/ENMC-10923)
- v1.6
  - Authorization of HTTP Requests (https://jira.mios.com/browse/ENMC-10854)
- v1.5
  - Custom AI model for package detection (https://jira.mios.com/browse/ENMC-10563)
- v1.4
  - Running object detection on GPU (https://jira.mios.com/browse/ENMC-10623)
- v1.3
  - Producing a special AI model for person/human detection (https://jira.mios.com/browse/ENMC-10562)
  - Producing a special AI model for package detection (https://jira.mios.com/browse/ENMC-10563)
- v1.2 (https://jira.mios.com/browse/EFWR-5138)
  - Added “global_threshold” value to API payload json structure
  - Added “debug” value to API payload json structure
  - Added “items” value for animal, vehicle, license plate, barcode, face recognition to API payload json structure. Thus, only cat and/or only car objects can be detected
- v1.1 (https://jira.mios.com/browse/ENMC-10463)
  - /jobs/count/{jobtype} : Listing the number of jobs [queued,started,finished,failed,deferred,scheduled,canceled]
  - /jobs/list/{jobtype} : Listing of jobs [queued,started,finished,failed,deferred,scheduled,canceled]
  - /job/status/{uuid} : Provides information about the status
  - /job/cancel/{uuid} : Allows the job to be canceled
  - /health : Provides information about status
  - /health/detail : Provides information about services
  - /workers : Provides information about workers
  - /version : Provides information application version
- v1.0 (https://jira.mios.com/browse/ENMC-10170)
  - Special version of the YoloWorld model produced for Ezlo
  - Changed related parameters to shorten the object detection time
  - License plate system is included in YoloWorld
  - Open ALPR system added in addition to ocr system (https://github.com/openalpr/openalpr)
  - Log records have been increased and detailed
  - Refactor process was performed to make the system more stable
  - In case of problems occurring in running steps, it was ensured to continue to the next step
  - Retention period for local log records increased to 30 days
  - Added to the configuration file so that some values can be changed externally
  - Added "/version" endpoint to find out which version of the application is running
- v0.94
  - The OCR system for license plate recognition has been upgraded to the Google Tesseract library (https://jira.mios.com/browse/ENMC-10155)
- v0.93
  - Face recognition should not be run if “person” is not present in the video (https://jira.mios.com/browse/ENMC-10136)
- v0.92
  - Fixed an error with the information of the person found in the Face Recognition section (https://jira.mios.com/browse/ENMC-10131)
- v0.91
  - Fixed JSON format validation on object detection (https://jira.mios.com/browse/ENMC-10054)
- v0.91
  - Fixed JSON format validation on object detection (https://jira.mios.com/browse/ENMC-10054)
- v0.9
  - Receiving a redirect response for the file to be object detected (https://jira.mios.com/browse/ENMC-9698)
  - File compression feature has been added to the File control step. It is set to True by default.
- v0.8 (https://jira.mios.com/browse/ENMC-9256)
  - Barcode / QRCode Recognition feature added
- v0.7 (https://jira.mios.com/browse/ENMC-9256)
  - Vehicle License Plate Recognition feature added
- v0.6 (https://jira.mios.com/browse/ENMC-9219)
  - Face Recognition feature added
- v0.5 (https://jira.mios.com/browse/ENMC-9173)
  - Hotzone Avalibility feature added
  - Reformatting
- v0.4 (https://jira.mios.com/browse/ENMC-9168)
  - Plugin required for limiting requests to endpoints
- v0.3 (https://jira.mios.com/browse/ENMC-9165)
  - Added package (box, package, parcel) detection feature (model: YoloWorld)
  - Confidence Threshold value imported into .env file
- v0.2 (https://jira.mios.com/browse/EFWR-4559)
  - Make a docker for cloudml
- v0.1
  - The files have been copied.

## Overview

The CloudML project was developed to detect objects such as package, animal, vehicle... on previously recorded MP4
files.

It creates a job with the data (payload) sent to the /object_detection endpoint and adds it to the queue.
When a request is sent to CloudML, FastApi (https://fastapi.tiangolo.com/) was chosen for the API mechanism that
processes the request.

It uses Redis server and RQ (https://python-rq.org/) library for job-queue structure.

Workers complete the incoming job in 4 steps. Before each step is completed, the next one is not started and the
triggering happens automatically.

#### 0_downloadFile:

Downloads the mp4 file whose URL is given in the payload to the savedVideoFies directory.

#### 1_fileCheck:

Checks that the downloaded mp4 file is a video file

#### 2_objectDetection:

Detects the objects to be detected in the downloaded mp4 file.

#### 3_sendResult:

Sends the detected objects in a predefined format to the corresponding response address

#### 4_cleanEnvironment:

Deletes unnecessary records of the job such as mp4 file, redis records

## Dockerize

The CloudML project runs with 3 services as can be seen in the docker-compose.yaml file.

#### 1: Redis:

Redis server does not necessarily have to be installed locally. The Redis service on another server can also be used.
This section can be deleted if an external Redis server is to be used.

#### 2: Fastapi

API container that will add the request to the queue and deliver the result when a request is sent to CloudML.
If the Redis service is not available locally, the relevant access information should be changed in the ".env" file.

#### 3: Worker

Container operating the previously mentioned steps
If the Redis service is not available locally, the relevant access information must be changed in the "command" section.

## Docker build

```
./dockerbuild.sh
```

## All Endpoints Documentation

- path: /

  - method: get
  - response: {"status": "success"}

- path: /health

  - method: get
  - response: {"status": DATETIME}

- path: /version

  - method: get
  - response: {"version": APPVERSION}

- path: /job_status/{uuid}
  - method: get
  - response:

```
{
    "uuid": "51fedb10-3918-46d2-8da8-d66b1892d079",
    "step00": {
        "uuid": "51fedb10-3918-46d2-8da8-d66b1892d079",
        "status": "finished",
        "result_type": "success",
        "created_at": "2024-05-07 10:49:22.960000+00:00",
        "enqueued_at": "2024-05-07 10:49:20.530757",
        "started_at": "2024-05-07 10:49:20.571091",
        "ended_at": "2024-05-07 10:49:22.949660",
        "exc_info": "None",
        "description": "step00_downloadFile_run",
        "return_value": "car-detection.mp4 downloaded"
    },
    "step01": {
        "uuid": "beb0e341-d4f0-4d1f-b617-797ab2214d99",
        "status": "finished",
        "result_type": "success",
        "created_at": "2024-05-07 10:49:24.080000+00:00",
        "enqueued_at": "2024-05-07 10:49:22.956784",
        "started_at": "2024-05-07 10:49:22.980149",
        "ended_at": "2024-05-07 10:49:24.068306",
        "exc_info": "None",
        "description": "step10_fileCheck_run",
        "return_value": "car-detection.mp4 mp4 check passed"
    },
    "step02": {
        "uuid": "71166114-e5c9-46cb-adf9-5a5c591e4a3b",
        "status": "finished",
        "result_type": "success",
        "created_at": "2024-05-07 10:50:02.290000+00:00",
        "enqueued_at": "2024-05-07 10:49:24.078720",
        "started_at": "2024-05-07 10:49:24.096895",
        "ended_at": "2024-05-07 10:50:02.279587",
        "exc_info": "None",
        "description": "step20_objectDetection_run",
        "return_value": "The object was successfully delivered as a result of detection"
    },
    "step03": {
        "uuid": "bb5fe093-6e39-43f1-b622-ae7645f1fbaa",
        "status": "finished",
        "result_type": "success",
        "created_at": "2024-05-07 10:50:02.871000+00:00",
        "enqueued_at": "2024-05-07 10:50:02.287765",
        "started_at": "2024-05-07 10:50:02.308919",
        "ended_at": "2024-05-07 10:50:02.844973",
        "exc_info": "None",
        "description": "step30_sendResult_run",
        "return_value": "detected objects: vehicle:car were successfully sent to : https://webhook.site/95520482-db1b-4e9a-a991-c9e770758b03"
    },
    "step04": {
        "uuid": "c774de04-d2a1-4442-b62b-1684b93cde77",
        "status": "finished",
        "result_type": "success",
        "created_at": "2024-05-07 10:50:02.954000+00:00",
        "enqueued_at": "2024-05-07 10:50:02.869176",
        "started_at": "2024-05-07 10:50:02.894648",
        "ended_at": "2024-05-07 10:50:02.950224",
        "exc_info": "None",
        "description": "step40_cleanEnvironment_run",
        "return_value": "deleted files in local directory and data on queue db"
    }
}
```

- path: /object_detection

  - method: post
  - payload: please visit https://confluence.mios.com/pages/viewpage.action?pageId=87240294

- response:

```
{
  "uuid":"fe76cee0-9de7-11ef-920d-ed661622ca36",
  "message":"job added to queue",
  "status":"success",
  "status_code":200
}
```

## Job result format

Please visit https://confluence.mios.com/pages/viewpage.action?pageId=87240294

## Sample mp4 files

- person-bicycle-car-detection.mp4

  - https://raw.githubusercontent.com/intel-iot-devkit/sample-videos/master/person-bicycle-car-detection.mp4

- face-demographics-walking-and-pause.mp4
  - https://raw.githubusercontent.com/intel-iot-devkit/sample-videos/master/face-demographics-walking-and-pause.mp4

## Local development

### Terminal 1

```
git clone ...
cd cloudml
source .venv/bin/activate
cd src
uvicorn main:app --host 127.0.0.1 --port 8080  --env-file ./.env.local
```

### Terminal 2

```
cd cloudml
source .venv/bin/activate
cd src
set -o allexport
source ./.env.local
set +o allexport
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
rq worker --url redis://localhost:6379/0 objectdetection
```

### Other

- Generate UUID and copy

  - for example with https://www.uuidgenerator.net
  - file->uuid paste

- Your unique URL copy
  - https://webhook.site/
  - response->callback

and run this command

Visit https://confluence.mios.com/pages/viewpage.action?pageId=87240294 for the current submittable data format.

```
curl --location 'http://127.0.0.1:8080/object_detection' \
--header 'Content-Type: application/json' \
--data '{
    "resources": {
    "method": "GET",
    "url": "https://raw.githubusercontent.com/intel-iot-devkit/sample-videos/master/person-bicycle-car-detection.mp4"
  },
  "file": {
    "uuid": "016d564b-0f55-43e5-8270-b0f68e4e54a2",
    "timestamp": 3252353232,
    "size": 354235235,
    "filename": "car-detection.mp4",
    "file_format": "mp4"
  },
  "db": {
    "elements": {},
    "configuration": {
      "package_detection": {
        "enabled": false
      },
      "animal_detection": {
        "enabled": true
      },
      "face_recognition": {
        "enabled": false
      },
      "barcode_detection": {
        "enabled": false
      },
      "vehicle_detection": {
        "enabled": true
      }
    },
    "files": {}
  },
  "reponse": {
    "callback": "https://webhook.site/95520482-db1b-4e9a-a991-c9e770758b03"
  }
}'
```

response json

```
{
  'status': 1,
  'detected': [
    {
      'uuid': '51fedb10-3918-46d2-8da8-d66b1892d079',
      'type': 'vehicle',
      'data': {
        'label': 'car',
        'label_element': '00000000-0000-0000-0000-000000000006',
        'timestamp': 1715078997,
        'milisec': 194,
        'class': 'vehicle',
        'conf': 0,
        'bbox': {
          'xmin': 0,
          'ymin': 0,
          'xmax': 0,
          'ymax': 0
        }
      }
    },
    {
      'uuid': '51fedb10-3918-46d2-8da8-d66b1892d079',
      'type': 'vehicle',
      'data': {
        'label': 'car',
        'label_element': '00000000-0000-0000-0000-000000000003',
        'timestamp': 1715078999,
        'milisec': 13,
        'class': 'vehicle',
        'conf': 0,
        'bbox': {
          'xmin': 0,
          'ymin': 0,
          'xmax': 0,
          'ymax': 0
        }
      }
    },
    {
      'uuid': '51fedb10-3918-46d2-8da8-d66b1892d079',
      'type': 'vehicle',
      'data': {
        'label': 'bicycle',
        'label_element': '00000000-0000-0000-0000-000000000004',
        'timestamp': 1715078999,
        'milisec': 964,
        'class': 'vehicle',
        'conf': 0,
        'bbox': {
          'xmin': 0,
          'ymin': 0,
          'xmax': 0,
          'ymax': 0
        }
      }
    }
  ]
}
```
