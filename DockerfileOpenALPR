FROM ubuntu:18.04

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update \
  && apt-get install -y --no-install-recommends \
    --option=Dpkg::Options::="--force-confdef" \
    --option=Dpkg::Options::="--force-confold" \
    build-essential \
    cmake \
    curl \
    git \
    libcurl3-dev \
    libleptonica-dev \
    liblog4cplus-dev \
    libopencv-dev \
    libtesseract-dev \
    wget \
    software-properties-common

RUN git clone --depth 1 https://github.com/osman-emek/openalpr.git /srv/openalpr
RUN rm -rf /srv/openalpr/.git

RUN mkdir /srv/openalpr/src/build
WORKDIR /srv/openalpr/src/build

RUN cmake -DCMAKE_INSTALL_PREFIX:PATH=/usr -DCMAKE_INSTALL_SYSCONFDIR:PATH=/etc .. && \
    make -j2 && \
    make install

#///////

RUN add-apt-repository -y ppa:deadsnakes/ppa

RUN apt-get update && apt-get install -y python3.8
RUN apt-get install -y python3-pip

RUN python3.8 -m pip --no-cache-dir install pip

RUN apt-get install -y python3-distutils
RUN apt-get install -y python3-setuptools
RUN apt-get install -y python3.8-venv

RUN python3.8 -m venv /venv

ENV PATH="/venv/bin:$PATH"

RUN python3.8 -m pip install --no-cache-dir pip --upgrade pip

RUN pip3 install --no-cache-dir fastapi
RUN pip3 install --no-cache-dir fastapi-cli

CMD ["fastapi", "run", "/srv/openalpr/code/main.py","--port","8181","--workers","4","--host","0.0.0.0"]
