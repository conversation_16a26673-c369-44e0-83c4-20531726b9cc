# Default Configuration for Object Detection Service
# This configuration provides sensible defaults for production deployment

# RabbitMQ Configuration
rabbitmq:
  host: "rabbitmq"
  port: 5672
  username: "rabbitusr"
  password: "1lqgEJU3VPyhg"
  vhost: "/"

# PostgreSQL Configuration
postgresql:
  host: "postgresql"
  port: 5432
  username: "postgres"
  password: "4GfW42eVb"
  database: "object_detection"
  echo: false
  # Automatically create additional indexes on database initialization
  auto_create_indexes: true

# Authentication Configuration
auth:
  enabled: false
  secret_key: "0968eb8c20c69225f15aa77d88d1552b4865b75a48ecc7289bd43c2293aac918829bfda206f38ae8ab669e6afad01c7f245f2c55bfe1c30f56e7ab67385dbb4a"
  algorithm: "HS256"

object_detection:
  device: "cpu"
  global_confidence_threshold: 0.65
  person_detection_classes:
    - "person"
  animal_detection_classes:
    - "cat"
    - "dog"
  vehicle_detection_classes:
    - "car"
    - "motorcycle"
    - "truck"
  package_detection_classes:
    - "parcel"
    - "box"
    - "bundle"
    - "packet"
    - "package"
    - "crate"
    - "carton"
    - "envelope"
    - "case"
    - "kit"
    - "bag"
  license_plate_detection_classes:
    - "license plate"

model_manager:
  max_cache_size: 4
  cleanup_threshold_hours: 1
  preload_on_startup: true
  preload_models:
    - "standard"
    - "package"
    - "custom_package"
    - "license_plate"

standard:
  model: "app/ezlomodels/ezlo-yolo-standard-11s.pt"
  skip_frame_ratio: 0.5

package:
  model: "app/ezlomodels/ezlo-yolowsv2.pt"
  skip_frame_ratio: 0.4

custom_package:
  model: "app/ezlomodels/ezlo-custom-package.pt"
  skip_frame_ratio: 0.4

# Face Recognition Configuration
face_recognition:
  const_tolerance: 0.71

# License Plate Recognition Configuration
license_plate_recognition:
  model: "app/ezlomodels/license_plate_detector.pt"

# Barcode Recognition Configuration
barcode_recognition:
  library: "opencv"
  # library: "pyzbar"

# Logging Configuration
logging:
  level: "INFO"
  format: "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s"
  file_path: ""
  max_file_size_mb: 0
  backup_count: 0

# Job Tracking Configuration
job_tracking:
  retention_days: 30

# File Processing Configuration
file_processing:
  video_temp_directory: "/code/savedvideofiles"

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  reload: false
  workers: 4

# Worker Configuration
worker:
  concurrency: 4
  prefetch_multiplier: 1
  max_tasks_per_child: 1000
  heartbeat_interval: 30
  status_report_interval: 60

# OpenTelemetry Configuration
open_telemetry:
  collector_endpoint: "https://ot.ezlo.com/traces"

# File Validation Configuration
file_validation:
  enabled: true
  max_file_size_mb: 1024
  timeout_seconds: 30
  allowed_content_types:
    - "video/mp4"
    - "video/x-m4v"
  allowed_extensions:
    - ".mp4"
    - ".m4v"
  temp_directory: "/tmp/object-detection"

# Monitoring Configuration
monitoring:
  enabled: true
  cache_ttl_seconds: 5
  default_page_size: 50
  max_page_size: 100
  rate_limit_rpm: 60
  authentication_required: false
  worker_heartbeat_timeout: 60

  # Model performance monitoring
  log_model_load_times: true
  log_cache_stats: true
  cache_stats_interval_minutes: 30
  collect_model_metrics: true

# Performance Configuration
performance:
  # GPU memory management
  gpu:
    # Clear GPU cache before loading new models
    clear_cache_before_load: true

    # Enable memory fraction limit (0.0-1.0, null for no limit)
    memory_fraction: null

    # Memory threshold for optimization (in MB)
    memory_threshold_mb: 1024

    # Enable automatic memory optimization
    auto_optimize: true

  # CPU optimization
  cpu:
    # Number of threads for CPU inference (null = auto-detect)
    num_threads: null

# Health Check Configuration
health_check:
  # Enable model health checks
  enabled: true

  # Health check interval (in minutes)
  interval_minutes: 60

  # Models to include in health checks
  models:
    - "standard"
    - "package"
    - "custom_package"
    - "license_plate"


# OCR Server
ocr_server:
  url: "http://ocrserver:8282/base64"

# OpenALPR Server
openalpr_server:
  url: "http://openalprserver:8181/alpr"
