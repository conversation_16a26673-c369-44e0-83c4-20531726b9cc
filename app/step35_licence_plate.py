import io
import os
import sys

import supervision as sv
from PIL import Image

from app.config.logger_factory import get_logger
from app.services.gpu_memory_manager import gpu_memory_manager
from app.step35_ocr_process import img_process, oalpr_plate, ocr_plate
from app.utils.logging_utils import filter_and_prepare_detection_result, log_and_raise_exception

logger = get_logger(__name__)


def step35_licence_plate_run(
    job_uuid,
    lplate_model,
    detection_file,
    plate_detection_filter_items,
    hotzone_zones_polygon,
    conf,
    device,
    vid_stride,
    plate_detection_country,
    ocr_server_url,
    openalpr_server_url,
):
    try:
        visualization_args = False
        total_found_classes = {}
        total_found_classes_np = {}

        # Use GPU memory context for optimized memory management
        device_id = 0 if device == "cuda" else None

        with gpu_memory_manager.memory_context(device_id=device_id or 0, clear_cache=True):
            result_lplate = lplate_model.track(
                detection_file,
                save=False,
                imgsz=640,
                conf=conf,
                stream=True,
                device=device,
                verbose=False,
                vid_stride=vid_stride,
                show=visualization_args,
                show_labels=visualization_args,
                show_conf=visualization_args,
                show_boxes=visualization_args,
                stream_buffer=True,
                visualize=False,
                augment=False,
                agnostic_nms=False,
                iou=0.5,
                retina_masks=False,
                save_crop=False,
            )

        for rlplate in result_lplate:
            result_detections = None
            detections = sv.Detections.from_ultralytics(rlplate)
            if len(hotzone_zones_polygon) > 0:
                for zone in hotzone_zones_polygon:
                    mask = zone.trigger(detections=detections)
                    zone_detections = detections[mask & (detections.confidence >= conf)]
                    result_detections = zone_detections
            else:
                result_detections = detections

            if result_detections is not None and len(result_detections.confidence) > 0:
                confIndx = 0
                for detected_conf in detections.confidence:
                    detected_conf = round(float(detected_conf), 2)
                    found_class = detections["class_name"][confIndx]

                    x1, y1, x2, y2 = detections.xyxy[confIndx]
                    x1 = int(x1)
                    y1 = int(y1)
                    x2 = int(x2)
                    y2 = int(y2)

                    frame = rlplate.plot(probs=False, conf=False, labels=False, boxes=False, masks=False)
                    license_plate_crop = frame[int(y1) : int(y2), int(x1) : int(x2), :]

                    with Image.fromarray(license_plate_crop) as img:
                        buffered = io.BytesIO()
                        img.save(buffered, format="PNG")
                        width_license_plate_crop = img.width

                    if detections.tracker_id is not None:
                        tracker_id = str(detections.tracker_id[confIndx])
                        found_class = found_class + "_" + tracker_id

                        if found_class in total_found_classes:
                            if width_license_plate_crop > total_found_classes[found_class]:
                                total_found_classes[found_class] = width_license_plate_crop
                                total_found_classes_np[found_class] = license_plate_crop
                        else:
                            total_found_classes[found_class] = width_license_plate_crop
                            total_found_classes_np[found_class] = license_plate_crop

                    confIndx = confIndx + 1
        # OCR
        license_plates = []
        for rkey in total_found_classes_np:
            selected_license_plate_crop = total_found_classes_np[rkey]
            proccessed_img = img_process(selected_license_plate_crop)

            label = ""

            try:
                label = ocr_plate(proccessed_img, ocr_server_url)
            except Exception as e:
                exc_type, exc_obj, exc_tb = sys.exc_info()
                fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
                respmes = f"Error step35_licence_plate_run ocr_plate {exc_type} {fname} {exc_tb.tb_lineno} {e}"
                logger.error(respmes)

            if len(label) <= 5 or label is None:
                try:
                    label = oalpr_plate(
                        proccessed_img,
                        job_uuid,
                        plate_detection_country,
                        openalpr_server_url,
                    )
                except Exception as e:
                    exc_type, exc_obj, exc_tb = sys.exc_info()
                    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
                    respmes = f"Error step35_licence_plate_run oalpr_plate {exc_type} {fname} {exc_tb.tb_lineno} {e}"
                    logger.error(respmes)

            if len(label) >= 5 and label is not None:
                license_plates.append(label)

        for lplateocr in license_plates:
            filter_and_prepare_detection_result(job_uuid, lplateocr, "licenseplate", plate_detection_filter_items)

    except Exception as err:
        log_and_raise_exception(err, "step35_licence_plate_run", logger)
