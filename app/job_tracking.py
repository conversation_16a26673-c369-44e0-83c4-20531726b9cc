from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError

from app.config.logger_factory import get_logger
from app.config.settings import config
from app.database import BaseRepository, Job, JobResult, db_manager

logger = get_logger(__name__)


class JobTracker(BaseRepository):
    def __init__(self):
        super().__init__(db_manager)
        self._init_postgresql()

    def _init_postgresql(self):
        try:
            logger.info("PostgreSQL job tracker initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL job tracker: {str(e)}")
            raise

    def job_exists(self, uuid: str) -> bool:
        try:
            with self.get_read_session() as session:
                job = session.query(Job).filter(Job.uuid == uuid).first()
                return job is not None
        except SQLAlchemyError as e:
            logger.error(f"Error checking job existence for UUID {uuid}: {str(e)}")
            return True  # Return True to prevent duplicate creation on error

    def create_job(self, uuid: str, ot_traceid: str, ot_spanid: str) -> bool:
        try:
            if self.job_exists(uuid):
                logger.warning(f"Job with UUID {uuid} already exists")
                return False

            retention_days = config.get("job_tracking.retention_days", 7)
            expires_at = datetime.utcnow() + timedelta(days=retention_days)

            with self.get_session() as session:
                job = Job(
                    uuid=uuid,
                    state="queued",
                    task_id=None,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    expires_at=expires_at,
                    ot_traceid=ot_traceid,
                    ot_spanid=ot_spanid,
                )
                session.add(job)
                # Session automatically commits due to context manager

            logger.info(f"Job {uuid} created successfully")
            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating job {uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating job {uuid}: {str(e)}")
            return False

    def create_job_result(self, job_uuid: str, result: dict) -> bool:
        try:
            with self.get_session() as session:
                # First check if job exists
                job = session.query(Job).filter(Job.uuid == job_uuid).first()
                if not job:
                    logger.warning(f"Job {job_uuid} not found, cannot insert result")
                    return False

                job_result = JobResult(job_uuid=job_uuid, result=result, created_at=datetime.utcnow())
                session.add(job_result)
                # Session automatically commits due to context manager

            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating JobResult for {job_uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating JobResult for {job_uuid}: {str(e)}")
            return False

    def update_job_state(
        self,
        uuid: str,
        step: str,
        state: str,
        error_message: Optional[str] = None,
        task_id: Optional[str] = None,
    ) -> bool:
        try:
            with self.get_session() as session:
                job = session.query(Job).filter(Job.uuid == uuid).first()
                if not job:
                    logger.warning(f"Job {uuid} not found for state update")
                    return False

                job.state = state
                job.step = step
                job.error_message = error_message
                job.updated_at = datetime.utcnow()
                if task_id:
                    job.task_id = task_id
                # Session automatically commits due to context manager

            return True
        except SQLAlchemyError as e:
            logger.error(f"Error updating job state for {uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating job state for {uuid}: {str(e)}")
            return False

    def get_job(self, uuid: str) -> Optional[Dict[str, Any]]:
        try:
            with self.get_read_session() as session:
                job = session.query(Job).filter(Job.uuid == uuid).first()
                if job:
                    return {
                        "uuid": job.uuid,
                        "state": job.state,
                        "step": job.step,
                        "task_id": job.task_id,
                        "created_at": job.created_at,
                        "updated_at": job.updated_at,
                        "expires_at": job.expires_at,
                        "ot_traceid": job.ot_traceid,
                        "ot_spanid": job.ot_spanid,
                        "error_message": job.error_message,
                    }
                return None
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving job {uuid}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving job {uuid}: {str(e)}")
            return None

    def get_job_state_by_uuid(self, job_uuid: str) -> Optional[str]:
        try:
            with self.get_read_session() as session:
                stmt = select(Job.state).where(Job.uuid == job_uuid)
                row = session.execute(stmt).scalar_one_or_none()
                return row  # state string or None
        except SQLAlchemyError as e:
            logger.error(f"Error fetching state for job {job_uuid}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching state for job {job_uuid}: {str(e)}")
            return None

    def get_jobs_by_state(self, state: str) -> List[Dict[str, Any]]:
        try:
            with self.get_read_session() as session:
                jobs = session.query(Job).filter(Job.state == state).all()
                return [
                    {
                        "uuid": job.uuid,
                        "state": job.state,
                        "step": job.step,
                        "task_id": job.task_id,
                        "created_at": job.created_at,
                        "updated_at": job.updated_at,
                        "expires_at": job.expires_at,
                        "ot_traceid": job.ot_traceid,
                        "ot_spanid": job.ot_spanid,
                        "error_message": job.error_message,
                    }
                    for job in jobs
                ]
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving jobs with state {state}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving jobs with state {state}: {str(e)}")
            return []

    def get_job_results(self, job_uuid: str) -> List[Dict[str, Any]]:
        try:
            with self.get_read_session() as session:
                stmt = (
                    select(
                        Job.state,
                        JobResult.result,
                        Job.error_message,
                        JobResult.result["class"].astext.label("class"),
                    )
                    .join(JobResult, Job.uuid == JobResult.job_uuid)
                    .where(Job.uuid == job_uuid)
                )
                rows = session.execute(stmt).all()

                if not rows:
                    logger.info(f"No results found for job {job_uuid}")
                    return []

                results = [
                    {
                        "state": state,
                        "result": result,
                        "error_message": error_message,
                        "class": class_name,
                    }
                    for state, result, error_message, class_name in rows
                ]
                return results

        except SQLAlchemyError as e:
            logger.error(f"Error fetching job results for {job_uuid}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching job results for {job_uuid}: {str(e)}")
            return []

    def get_job_count_by_state(self, state: str) -> int:
        try:
            with self.get_read_session() as session:
                return session.query(Job).filter(Job.state == state).count()
        except SQLAlchemyError as e:
            logger.error(f"Error counting jobs with state {state}: {str(e)}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error counting jobs with state {state}: {str(e)}")
            return 0

    def cleanup_expired_jobs(self) -> int:
        try:
            with self.get_session() as session:
                current_time = datetime.utcnow()
                expired_jobs = session.query(Job).filter(Job.expires_at < current_time).all()
                count = len(expired_jobs)

                for job in expired_jobs:
                    session.delete(job)
                # Session automatically commits due to context manager

                logger.info(f"Cleaned up {count} expired jobs from PostgreSQL")
                return count
        except SQLAlchemyError as e:
            logger.error(f"Error cleaning up expired jobs: {str(e)}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error cleaning up expired jobs: {str(e)}")
            return 0


job_tracker = JobTracker()
