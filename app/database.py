from contextlib import contextmanager
from datetime import datetime
from typing import Generator

from sqlalchemy import Column, DateTime, ForeignKey, Integer, String, Text, create_engine
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship, sessionmaker

from app.config.logger_factory import get_logger
from app.config.settings import config

logger = get_logger(__name__)

Base = declarative_base()


class Job(Base):
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    uuid = Column(String(36), unique=True, nullable=False, index=True)
    state = Column(String(50), nullable=False, index=True)
    step = Column(String(50), nullable=True)
    task_id = Column(String(255), nullable=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False, index=True)
    ot_traceid = Column(String(255), nullable=True, index=True)
    ot_spanid = Column(String(255), nullable=True)
    error_message = Column(Text, nullable=True)

    results = relationship("JobResult", back_populates="job")

    def __repr__(self):
        return f"<Job(uuid='{self.uuid}', state='{self.state}', step='{self.step}')>"


class JobResult(Base):
    __tablename__ = "job_results"

    id = Column(Integer, primary_key=True, index=True)
    job_uuid = Column(String(36), ForeignKey("jobs.uuid"), nullable=False, index=True)
    result = Column(JSONB, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)

    job = relationship("Job", back_populates="results")


class DatabaseManager:
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._init_database()

    def _init_database(self):
        try:
            host = config.get("postgresql.host", "postgresql")
            port = config.get("postgresql.port", 5432)
            username = config.get("postgresql.username", "postgres")
            password = config.get("postgresql.password", "password")
            database = config.get("postgresql.database", "object_detection")
            echo_value = config.get("postgresql.echo", False)

            connection_string = f"postgresql://{username}:{password}@{host}:{port}/{database}"

            self.engine = create_engine(connection_string, pool_pre_ping=True, pool_recycle=300, echo=echo_value, pool_size=10, max_overflow=20)

            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

            # Create tables first
            Base.metadata.create_all(bind=self.engine)

            # Create additional indexes after table creation (if enabled)
            if config.get("postgresql.auto_create_indexes", True):
                self._create_additional_indexes()
            else:
                logger.info("Automatic index creation disabled in configuration")

            logger.info("PostgreSQL database manager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL database manager: {str(e)}")
            raise

    def _create_additional_indexes(self):
        try:
            from sqlalchemy import text

            additional_indexes = [
                # Composite indexes for better query performance
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_state_created_at ON jobs(state, created_at)",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_expires_at_state ON jobs(expires_at, state)",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_job_results_job_uuid_created_at ON job_results(job_uuid, created_at)",
                # GIN index for JSON search
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_job_results_result_gin ON job_results USING GIN(result)",
                # Partial indexes for specific use cases
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_active_states ON jobs(state, updated_at) WHERE state IN ('queued', 'running', 'processing', 'started')",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_error_state ON jobs(updated_at, error_message) WHERE state = 'failed' AND error_message IS NOT NULL",
                # Partial indexes for non-null values
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_task_id_partial ON jobs(task_id) WHERE task_id IS NOT NULL",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_ot_traceid_partial ON jobs(ot_traceid) WHERE ot_traceid IS NOT NULL",
            ]

            with self.session_scope() as session:
                for index_sql in additional_indexes:
                    try:
                        index_name = index_sql.split("idx_")[1].split(" ")[0] if "idx_" in index_sql else "unknown"
                        logger.info(f"Creating additional index: {index_name}")
                        session.execute(text(index_sql))
                        session.commit()
                    except Exception as e:
                        # Index might already exist or creation might fail - log but continue
                        logger.warning(f"Could not create index (might already exist): {str(e)}")
                        session.rollback()
                        continue

            logger.info("Additional database indexes created successfully")

        except Exception as e:
            logger.error(f"Failed to create additional indexes: {str(e)}")
            # Don't raise - this shouldn't prevent database initialization

    def get_session(self) -> Session:
        return self.SessionLocal()

    @contextmanager
    def session_scope(self, auto_commit: bool = True) -> Generator[Session, None, None]:
        session = self.SessionLocal()
        try:
            yield session
            if auto_commit:
                session.commit()
                logger.debug("Database transaction committed successfully")
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Database error occurred, rolling back transaction: {str(e)}")
            raise
        except Exception as e:
            session.rollback()
            logger.error(f"Unexpected error occurred, rolling back transaction: {str(e)}")
            raise
        finally:
            session.close()
            logger.debug("Database session closed")

    @contextmanager
    def read_only_session(self) -> Generator[Session, None, None]:
        session = self.SessionLocal()
        try:
            yield session
        except SQLAlchemyError as e:
            logger.error(f"Database error during read operation: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during read operation: {str(e)}")
            raise
        finally:
            session.rollback()  # Ensure no changes are committed for read-only operations
            session.close()
            logger.debug("Read-only database session closed")

    def close(self):
        if self.engine:
            self.engine.dispose()
            logger.info("Database engine disposed successfully")


db_manager = DatabaseManager()


def get_db() -> Generator[Session, None, None]:
    with db_manager.session_scope() as session:
        yield session


def get_read_only_db() -> Generator[Session, None, None]:
    with db_manager.read_only_session() as session:
        yield session


class BaseRepository:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    @contextmanager
    def get_session(self, auto_commit: bool = True) -> Generator[Session, None, None]:
        with self.db_manager.session_scope(auto_commit=auto_commit) as session:
            yield session

    @contextmanager
    def get_read_session(self) -> Generator[Session, None, None]:
        with self.db_manager.read_only_session() as session:
            yield session
