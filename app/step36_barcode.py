import cv2 as cv

from app.config.logger_factory import get_logger
from app.utils.logging_utils import filter_and_prepare_detection_result

logger = get_logger(__name__)


def step36_barcode_run(job_uuid, source, barcode_detection_filter_items):
    try:
        qr_decoder = cv.QRCodeDetector()
        cap = cv.VideoCapture(source)
        qrCodes = []
        while cap.isOpened():
            ret, frame = cap.read()
            if ret:
                retval, decoded_info, points, straight_qrcode = qr_decoder.detectAndDecodeMulti(frame)
                if retval:
                    if len(decoded_info) > 0:
                        for qrc in decoded_info:
                            qrcn = qrc.strip()
                            if len(qrcn) > 0:
                                qrCodes.append(qrcn)
            else:
                cap.release()

        cap.release()

        qrCodesU = set(qrCodes)
        for qr in qrCodesU:
            filter_and_prepare_detection_result(job_uuid, qr, "barcode", barcode_detection_filter_items)
    except Exception as err:
        from app.utils.logging_utils import log_and_raise_exception

        log_and_raise_exception(err, "step36_barcode_run", logger)
