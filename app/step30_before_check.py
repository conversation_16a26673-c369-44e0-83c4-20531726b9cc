import os

from app.config.logger_factory import get_logger
from app.step31_objectDetection import step31_objectDetection_run
from app.utils.logging_utils import StepExecutionContext

logger = get_logger(__name__)


def step30_before_check_run(job_uuid, task_params_dict: dict, detection_file):
    with StepExecutionContext(job_uuid, "objectdetection", logger):
        if not os.path.isfile(detection_file):
            raise Exception(f"File not found: {detection_file}")

        step31_objectDetection_run(job_uuid, task_params_dict, detection_file)
