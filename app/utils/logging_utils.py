import os
import sys
from typing import Any, Callable, Optional, TypeVar

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker

logger = get_logger(__name__)

F = TypeVar("F", bound=Callable[..., Any])


def format_exception_details(exc: Exception, function_name: str = "") -> str:
    exc_type, exc_obj, exc_tb = sys.exc_info()
    if exc_tb:
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        line_no = str(exc_tb.tb_lineno)
    else:
        fname = "unknown"
        line_no = "unknown"

    if function_name:
        return f"{function_name} Exception: {str(exc)} | file: {fname} | line: {line_no}"
    else:
        return f"Exception: {str(exc)} | file: {fname} | line: {line_no}"


def log_and_raise_exception(
    exc: Exception,
    function_name: str = "",
    logger_instance: Optional[Any] = None,
) -> None:
    if logger_instance is None:
        logger_instance = logger

    error_msg = format_exception_details(exc, function_name)
    logger_instance.error(error_msg)
    raise Exception(error_msg) from exc


def filter_and_prepare_detection_result(
    job_uuid: str,
    item: str,
    category: str,
    filter_items: Optional[list] = None,
    item_uuid: Optional[str] = None,
) -> None:

    from app.helper import prepare_detection_result

    if filter_items is not None:
        if item.lower() in [filter_item.lower() for filter_item in filter_items]:
            if item_uuid:
                prepare_detection_result(job_uuid, item, category, item_uuid)
            else:
                prepare_detection_result(job_uuid, item, category)
    else:
        if item_uuid:
            prepare_detection_result(job_uuid, item, category, item_uuid)
        else:
            prepare_detection_result(job_uuid, item, category)


class StepExecutionContext:

    def __init__(
        self,
        job_uuid: str,
        step_name: str,
        logger_instance: Optional[Any] = None,
    ):
        self.job_uuid = job_uuid
        self.step_name = step_name
        self.logger = logger_instance or logger

    def __enter__(self):
        self.logger.info(f"Starting step: {self.step_name}")
        job_tracker.update_job_state(self.job_uuid, self.step_name, "started")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.logger.error(f"Failed step: {self.step_name}")
            job_tracker.update_job_state(self.job_uuid, self.step_name, "failed")
            # Re-raise with formatted message
            if exc_val:
                error_msg = format_exception_details(exc_val, self.step_name)
                self.logger.error(error_msg)
                # Don't suppress the exception, let it propagate
                return False
        else:
            self.logger.info(f"Completed step: {self.step_name}")
            job_tracker.update_job_state(self.job_uuid, self.step_name, "finished")

        return False  # Don't suppress exceptions
