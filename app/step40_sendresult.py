import json
import os
import sys
from datetime import datetime
from decimal import Decimal

import requests
from requests.exceptions import ConnectionError, HTTPError, RequestException, Timeout

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker
from app.models import TaskParameters

logger = get_logger(__name__)


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if hasattr(obj, "dict"):
            # Pydantic model
            return obj.dict()
        elif hasattr(obj, "__dict__"):
            # Regular object
            return obj.__dict__
        elif isinstance(obj, datetime):
            # DateTime objects
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            # Decimal objects
            return float(obj)
        elif hasattr(obj, "tolist"):
            # NumPy arrays
            return obj.tolist()
        return super().default(obj)


def step40_sendResult_run(job_uuid: str, task_params: TaskParameters, job_state=None, error_msg: str = None):
    try:
        job_tracker.update_job_state(job_uuid, "sendresult", "started")
        rows = job_tracker.get_job_results(job_uuid)
        detectedData = []
        for rec in rows:
            if job_state is None:
                job_state = rec["state"]

            if error_msg is None:
                error_msg = rec["error_message"]

            result = rec["result"]
            class_value = rec["class"]
            if job_state == "failed":
                payload = {
                    "uuid": str(job_uuid),
                    "type": str(class_value),
                    "data": result,
                    "error_message": error_msg,
                }
            else:
                payload = {
                    "uuid": str(job_uuid),
                    "type": str(class_value),
                    "data": result,
                }

            detectedData.append(payload)

        responseJson = {
            "ot": {
                "trace_id": str(task_params.ot.trace_id),
                "span_id": str(task_params.ot.span_id),
            },
            "status": 1,
            "detected": detectedData,
            "uuid": job_uuid,
        }
        jsonResult = json.dumps(responseJson)
        headers = {"Content-Type": "application/json"}
        response = requests.post(task_params.response.callback, data=jsonResult, headers=headers)
        if response.ok:
            logger.debug(f"step40_sendResult_run sending result to: {task_params.resources.url}, jsonResult: {jsonResult} status_code:{response.status_code}")
        else:
            response_message = f"step40_sendResult_run.callback_response could not be sent detected objects: {jsonResult} were not sent to: {task_params.resources.url} text:{response.text} reason:{response.reason} status_code:{response.status_code} uuid:{job_uuid}"
            logger.error(response_message)
            raise Exception(response_message)
    except Timeout as e:
        error_msg = f"Failed to send results to callback timeout {str(e)}"
        job_tracker.update_job_state(job_uuid, "sendresult", "failed", error_msg)
        raise Exception()
    except ConnectionError as e:
        error_msg = f"Failed to send results to callback connection error: {str(e)}"
        job_tracker.update_job_state(job_uuid, "sendresult", "failed", error_msg)
        raise Exception(error_msg)
    except HTTPError as e:
        error_msg = f"Failed to send results to callback http error: {str(e)}"
        job_tracker.update_job_state(job_uuid, "sendresult", "failed", error_msg)
        raise Exception(error_msg)
    except RequestException as e:
        error_msg = f"Failed to send results to callback request exception: {str(e)}"
        job_tracker.update_job_state(job_uuid, "sendresult", "failed", error_msg)
        raise Exception(error_msg)
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        file_name = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        error_msg = f"Unexpected error while sending results to callback url:{task_params.response.callback} etype:{exc_type} file:{file_name} line:{exc_tb.tb_lineno} err:{str(e)}"
        job_tracker.update_job_state(job_uuid, "sendresult", "failed", error_msg)
        raise Exception(error_msg)
    finally:
        job_tracker.update_job_state(job_uuid, "sendresult", "finished")
