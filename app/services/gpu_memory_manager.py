import gc
import threading
import time
from contextlib import contextmanager
from typing import Dict, Optional

import torch

from app.config.logger_factory import get_logger
from app.config.settings import config

logger = get_logger(__name__)


class GPUMemoryManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, "_initialized"):
            return

        self._device_available = torch.cuda.is_available()
        self._device_count = torch.cuda.device_count() if self._device_available else 0
        self._memory_fraction = config.get("performance.gpu.memory_fraction", None)
        self._clear_cache_before_load = config.get("performance.gpu.clear_cache_before_load", True)
        self._memory_threshold = config.get("performance.gpu.memory_threshold_mb", 1024)  # MB
        self._monitoring_enabled = config.get("monitoring.collect_model_metrics", True)

        # Memory usage tracking
        self._memory_stats = {}
        self._last_cleanup_time = time.time()
        self._cleanup_interval = 300  # 5 minutes

        self._initialized = True

        if self._device_available:
            self._setup_memory_management()
            logger.info(f"GPU Memory Manager initialized - Devices: {self._device_count}")
        else:
            logger.info("GPU Memory Manager initialized - CPU only mode")

    def _setup_memory_management(self):
        try:
            # Set memory fraction if configured
            if self._memory_fraction is not None:
                for device_id in range(self._device_count):
                    torch.cuda.set_per_process_memory_fraction(self._memory_fraction, device=device_id)
                logger.info(f"Set GPU memory fraction to {self._memory_fraction}")

            # Log initial memory status
            self._log_memory_status()

        except Exception as e:
            logger.error(f"Failed to setup GPU memory management: {str(e)}")

    def is_gpu_available(self) -> bool:
        return self._device_available

    def get_memory_info(self, device_id: int = 0) -> Dict[str, float]:
        if not self._device_available:
            return {"total": 0, "allocated": 0, "cached": 0, "free": 0}

        try:
            torch.cuda.set_device(device_id)

            # Get memory info in bytes, convert to MB
            total = torch.cuda.get_device_properties(device_id).total_memory / 1024**2
            allocated = torch.cuda.memory_allocated(device_id) / 1024**2
            cached = torch.cuda.memory_reserved(device_id) / 1024**2
            free = total - allocated

            return {"total": round(total, 2), "allocated": round(allocated, 2), "cached": round(cached, 2), "free": round(free, 2), "utilization": round((allocated / total) * 100, 2)}
        except Exception as e:
            logger.error(f"Failed to get memory info for device {device_id}: {str(e)}")
            return {"total": 0, "allocated": 0, "cached": 0, "free": 0, "utilization": 0}

    def get_all_devices_memory_info(self) -> Dict[int, Dict[str, float]]:
        if not self._device_available:
            return {}

        memory_info = {}
        for device_id in range(self._device_count):
            memory_info[device_id] = self.get_memory_info(device_id)

        return memory_info

    @contextmanager
    def memory_context(self, device_id: int = 0, clear_cache: bool = None):
        if not self._device_available:
            yield
            return

        clear_cache = clear_cache if clear_cache is not None else self._clear_cache_before_load

        # Pre-operation setup
        original_device = torch.cuda.current_device()
        torch.cuda.set_device(device_id)

        if clear_cache:
            self.clear_cache(device_id)

        # Track memory before operation
        memory_before = self.get_memory_info(device_id) if self._monitoring_enabled else None

        try:
            yield
        finally:
            # Post-operation cleanup
            if self._monitoring_enabled and memory_before:
                memory_after = self.get_memory_info(device_id)
                self._track_memory_usage(device_id, memory_before, memory_after)

            # Restore original device
            torch.cuda.set_device(original_device)

            # Optional cleanup
            if clear_cache:
                self.clear_cache(device_id)

    def clear_cache(self, device_id: Optional[int] = None):
        if not self._device_available:
            return

        try:
            if device_id is not None:
                torch.cuda.set_device(device_id)
                torch.cuda.empty_cache()
                logger.debug(f"Cleared GPU cache for device {device_id}")
            else:
                # Clear cache for all devices
                for dev_id in range(self._device_count):
                    torch.cuda.set_device(dev_id)
                    torch.cuda.empty_cache()
                logger.debug("Cleared GPU cache for all devices")

        except Exception as e:
            logger.error(f"Failed to clear GPU cache: {str(e)}")

    def force_garbage_collection(self):
        try:
            # Python garbage collection
            gc.collect()

            # GPU cache clearing
            if self._device_available:
                self.clear_cache()

            logger.debug("Forced garbage collection completed")

        except Exception as e:
            logger.error(f"Failed to force garbage collection: {str(e)}")

    def check_memory_pressure(self, device_id: int = 0, threshold_mb: Optional[float] = None) -> bool:
        if not self._device_available:
            return False

        threshold_mb = threshold_mb or self._memory_threshold
        memory_info = self.get_memory_info(device_id)

        return memory_info["allocated"] > threshold_mb

    def optimize_memory_usage(self, device_id: int = 0):
        if not self._device_available:
            return

        try:
            memory_before = self.get_memory_info(device_id)

            # Force garbage collection
            self.force_garbage_collection()

            memory_after = self.get_memory_info(device_id)

            freed_mb = memory_before["allocated"] - memory_after["allocated"]
            if freed_mb > 0:
                logger.info(f"Optimized GPU memory: freed {freed_mb:.2f} MB on device {device_id}")

        except Exception as e:
            logger.error(f"Failed to optimize memory usage: {str(e)}")

    def _track_memory_usage(self, device_id: int, memory_before: Dict, memory_after: Dict):
        try:
            memory_used = memory_after["allocated"] - memory_before["allocated"]

            if device_id not in self._memory_stats:
                self._memory_stats[device_id] = {"total_operations": 0, "total_memory_used": 0, "peak_memory": 0, "last_operation": time.time()}

            stats = self._memory_stats[device_id]
            stats["total_operations"] += 1
            stats["total_memory_used"] += memory_used
            stats["peak_memory"] = max(stats["peak_memory"], memory_after["allocated"])
            stats["last_operation"] = time.time()

            # Log if memory usage is significant
            if memory_used > 100:  # More than 100MB
                logger.debug(f"GPU memory usage: {memory_used:.2f} MB on device {device_id}")

        except Exception as e:
            logger.error(f"Failed to track memory usage: {str(e)}")

    def _log_memory_status(self):
        if not self._device_available:
            return

        try:
            for device_id in range(self._device_count):
                memory_info = self.get_memory_info(device_id)
                logger.info(f"GPU {device_id} Memory: " f"{memory_info['allocated']:.0f}MB/{memory_info['total']:.0f}MB " f"({memory_info['utilization']:.1f}% used)")
        except Exception as e:
            logger.error(f"Failed to log memory status: {str(e)}")

    def get_memory_stats(self) -> Dict:
        stats = {"gpu_available": self._device_available, "device_count": self._device_count, "memory_fraction": self._memory_fraction, "current_memory": self.get_all_devices_memory_info(), "usage_stats": self._memory_stats.copy()}

        return stats

    def periodic_cleanup(self):
        current_time = time.time()

        if current_time - self._last_cleanup_time > self._cleanup_interval:
            try:
                # Check memory pressure on all devices
                for device_id in range(self._device_count):
                    if self.check_memory_pressure(device_id):
                        logger.info(f"High memory pressure detected on device {device_id}, optimizing...")
                        self.optimize_memory_usage(device_id)

                self._last_cleanup_time = current_time

            except Exception as e:
                logger.error(f"Failed periodic cleanup: {str(e)}")


# Global singleton instance
gpu_memory_manager = GPUMemoryManager()
