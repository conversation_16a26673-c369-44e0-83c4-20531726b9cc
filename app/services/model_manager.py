import threading
import time
from enum import Enum
from pathlib import Path
from typing import Any, Dict, Optional

import torch
from ultralytics import <PERSON>OL<PERSON>

from app.config.logger_factory import get_logger
from app.config.settings import config

logger = get_logger(__name__)


class ModelType(str, Enum):
    STANDARD = "standard"
    PACKAGE = "package"
    CUSTOM_PACKAGE = "custom_package"
    LICENSE_PLATE = "license_plate"


class ModelInfo:
    def __init__(self, model: YOLO, path: str, load_time: float, last_used: float):
        self.model = model
        self.path = path
        self.load_time = load_time
        self.last_used = last_used
        self.usage_count = 0

    def update_usage(self):
        self.last_used = time.time()
        self.usage_count += 1


class ModelManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, "_initialized"):
            return

        self._models: Dict[str, ModelInfo] = {}
        self._model_lock = threading.RLock()
        self._device = self._get_device()
        self._max_cache_size = config.get("model_manager.max_cache_size", 4)
        self._cleanup_threshold = config.get("model_manager.cleanup_threshold_hours", 1)
        self._initialized = True

        logger.info(f"ModelManager initialized with device: {self._device}")

    def _get_device(self) -> str:
        device_config = config.get("object_detection.device", "gpu")

        if device_config == "cpu":
            return "cpu"
        elif device_config == "gpu" and torch.cuda.is_available():
            return "cuda"
        else:
            logger.warning("GPU requested but not available, falling back to CPU")
            return "cpu"

    def _get_model_key(self, model_path: str, device: Optional[str] = None) -> str:
        device = device or self._device
        return f"{model_path}:{device}"

    def _load_model(self, model_path: str, device: Optional[str] = None) -> YOLO:
        device = device or self._device

        if not Path(model_path).exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")

        start_time = time.time()

        try:
            # Import GPU memory manager here to avoid circular imports
            from app.services.gpu_memory_manager import gpu_memory_manager

            # Use GPU memory context for model loading
            device_id = 0 if device == "cuda" else None

            with gpu_memory_manager.memory_context(device_id=device_id or 0, clear_cache=True):
                logger.info(f"Loading model: {model_path} on device: {device}")
                model = YOLO(model_path)

                # Move model to specified device
                if hasattr(model, "to"):
                    model.to(device)

                load_time = time.time() - start_time
                logger.info(f"Model loaded successfully in {load_time:.2f}s: {model_path}")

                return model

        except Exception as e:
            logger.error(f"Failed to load model {model_path}: {str(e)}")
            raise

    def get_model(self, model_path: str, device: Optional[str] = None) -> YOLO:
        device = device or self._device
        model_key = self._get_model_key(model_path, device)

        with self._model_lock:
            # Check if model is already cached
            if model_key in self._models:
                model_info = self._models[model_key]
                model_info.update_usage()
                logger.debug(f"Using cached model: {model_path} (usage: {model_info.usage_count})")
                return model_info.model

            # Check cache size and cleanup if necessary
            if len(self._models) >= self._max_cache_size:
                self._cleanup_old_models()

            # Load new model
            try:
                model = self._load_model(model_path, device)
                load_time = time.time()

                model_info = ModelInfo(model=model, path=model_path, load_time=load_time, last_used=load_time)
                model_info.update_usage()

                self._models[model_key] = model_info
                logger.info(f"Model cached: {model_path} (cache size: {len(self._models)})")

                return model

            except Exception as e:
                logger.error(f"Failed to load and cache model {model_path}: {str(e)}")
                raise RuntimeError(f"Model loading failed: {str(e)}")

    def get_standard_model(self, model_path: Optional[str] = None, device: Optional[str] = None) -> YOLO:
        model_path = model_path or config.get("standard.model", "app/ezlomodels/ezlo-yolo-standard-11s.pt")
        return self.get_model(model_path, device)

    def get_package_model(self, model_path: Optional[str] = None, device: Optional[str] = None) -> YOLO:
        model_path = model_path or config.get("package.model", "app/ezlomodels/ezlo-yolowsv2.pt")
        return self.get_model(model_path, device)

    def get_custom_package_model(self, model_path: Optional[str] = None, device: Optional[str] = None) -> YOLO:
        model_path = model_path or config.get("custom_package.model", "app/ezlomodels/ezlo-custom-package.pt")
        return self.get_model(model_path, device)

    def get_license_plate_model(self, model_path: Optional[str] = None, device: Optional[str] = None) -> YOLO:
        model_path = model_path or config.get("license_plate_recognition.model", "app/ezlomodels/license_plate_detector.pt")
        return self.get_model(model_path, device)

    def _cleanup_old_models(self):
        if not self._models:
            return

        current_time = time.time()
        threshold_seconds = self._cleanup_threshold * 3600  # Convert hours to seconds

        # Find models to remove (oldest and least used)
        models_to_remove = []
        for key, model_info in self._models.items():
            if current_time - model_info.last_used > threshold_seconds:
                models_to_remove.append(key)

        # If no old models, remove least recently used
        if not models_to_remove and len(self._models) >= self._max_cache_size:
            sorted_models = sorted(self._models.items(), key=lambda x: (x[1].last_used, x[1].usage_count))
            models_to_remove = [sorted_models[0][0]]

        # Remove selected models
        for key in models_to_remove:
            model_info = self._models.pop(key)
            logger.info(f"Removed cached model: {model_info.path} (usage: {model_info.usage_count})")

            # Clear GPU memory if applicable
            if self._device == "cuda":
                # Import GPU memory manager here to avoid circular imports
                from app.services.gpu_memory_manager import gpu_memory_manager

                del model_info.model
                gpu_memory_manager.clear_cache(device_id=0)

    def clear_cache(self):
        with self._model_lock:
            logger.info(f"Clearing model cache ({len(self._models)} models)")
            self._models.clear()

            if self._device == "cuda":
                torch.cuda.empty_cache()

    def get_cache_stats(self) -> Dict[str, Any]:
        with self._model_lock:
            stats = {"cached_models": len(self._models), "max_cache_size": self._max_cache_size, "device": self._device, "models": {}}

            for key, model_info in self._models.items():
                stats["models"][key] = {"path": model_info.path, "usage_count": model_info.usage_count, "last_used": model_info.last_used, "load_time": model_info.load_time}

            return stats

    def preload_models(self, model_paths: Optional[Dict[str, str]] = None):
        if model_paths is None:
            model_paths = {"standard": config.get("standard.model", "app/ezlomodels/ezlo-yolo-standard-11s.pt"), "package": config.get("package.model", "app/ezlomodels/ezlo-yolowsv2.pt"), "custom_package": config.get("custom_package.model", "app/ezlomodels/ezlo-custom-package.pt"), "license_plate": config.get("license_plate_recognition.model", "app/ezlomodels/license_plate_detector.pt")}

        logger.info("Preloading models...")
        for model_type, model_path in model_paths.items():
            try:
                if Path(model_path).exists():
                    self.get_model(model_path)
                    logger.info(f"Preloaded {model_type} model: {model_path}")
                else:
                    logger.warning(f"Model file not found for preloading: {model_path}")
            except Exception as e:
                logger.error(f"Failed to preload {model_type} model: {str(e)}")


# Global singleton instance
model_manager = ModelManager()
