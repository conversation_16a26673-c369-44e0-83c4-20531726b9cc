import os
import sys

import supervision as sv

from app.config.logger_factory import get_logger
from app.helper import typed_append
from app.services.gpu_memory_manager import gpu_memory_manager

logger = get_logger(__name__)


def step33_package_detection_yoloworld_run(
    job_uuid,
    model,
    frame,
    hotzone_zones_polygon,
    conf,
    package_skip_frames,
    device,
    response_callback,
):
    try:
        # for Nvidia A16 & workers=20 --> batch=1
        batch_size = 4
        visualization_args = False
        total_found_classes = {}

        # Use GPU memory context for optimized memory management
        device_id = 0 if device == "cuda" else None

        with gpu_memory_manager.memory_context(device_id=device_id or 0, clear_cache=True):
            results_standard = model.predict(
                frame,
                save=False,
                imgsz=640,
                conf=conf,
                stream=True,
                batch=batch_size,
                vid_stride=package_skip_frames,
                device=device,
                verbose=False,
                show=visualization_args,
                show_labels=visualization_args,
                show_conf=visualization_args,
                show_boxes=visualization_args,
                stream_buffer=True,
                visualize=False,
                augment=False,
                agnostic_nms=False,
                retina_masks=False,
                save_crop=False,
            )

        for rstandard in results_standard:
            result_detections = ()
            detections = sv.Detections.from_ultralytics(rstandard)
            if len(hotzone_zones_polygon) > 0:
                for zone in hotzone_zones_polygon:
                    mask = zone.trigger(detections=detections)
                    zone_detections = detections[mask & (detections.confidence >= conf)]
                    result_detections = typed_append(result_detections, zone_detections)
            else:
                result_detections = typed_append(result_detections, detections)

            for result_detection in result_detections:
                if result_detection is not None and len(result_detection.confidence) > 0:
                    confIndx = 0

                    # FOUND
                    for current_conf in result_detection.confidence:
                        current_conf = round(float(current_conf), 3)
                        found_class = result_detection["class_name"][confIndx]
                        total_found_classes[found_class] = current_conf
                        break
                    break

        return total_found_classes
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"step33_package_detection_yoloworld_run Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
