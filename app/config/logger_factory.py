import logging
import threading
from contextvars import <PERSON><PERSON><PERSON><PERSON>
from logging.handlers import <PERSON><PERSON>tingFileHandler
from typing import Dict, Optional

from app.config.settings import config

# Context variables for storing request-specific information
job_uuid_context: ContextVar[Optional[str]] = ContextVar("job_uuid", default=None)
trace_id_context: ContextVar[Optional[str]] = ContextVar("trace_id", default=None)
span_id_context: ContextVar[Optional[str]] = ContextVar("span_id", default=None)


class ContextualFormatter(logging.Formatter):
    def format(self, record):
        # Add context information to the log record
        record.job_uuid = job_uuid_context.get() or "N/A"
        record.trace_id = trace_id_context.get() or "N/A"
        record.span_id = span_id_context.get() or "N/A"

        return super().format(record)


class ContextualLogger:
    def __init__(self, logger: logging.Logger):
        self._logger = logger

    def _log_with_context(self, level: int, msg: str, *args, **kwargs):
        job_uuid = job_uuid_context.get()
        trace_id = trace_id_context.get()

        # Add context prefix if available
        context_parts = []
        if job_uuid:
            context_parts.append(f"job_uuid:{job_uuid}")
        if trace_id:
            context_parts.append(f"trace_id:{trace_id}")

        if context_parts:
            context_prefix = f"[{' '.join(context_parts)}] "
            msg = f"{context_prefix}{msg}"

        self._logger.log(level, msg, *args, **kwargs)

    def debug(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.DEBUG, msg, *args, **kwargs)

    def info(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.INFO, msg, *args, **kwargs)

    def warning(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.WARNING, msg, *args, **kwargs)

    def error(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.ERROR, msg, *args, **kwargs)

    def critical(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.CRITICAL, msg, *args, **kwargs)

    def exception(self, msg: str, *args, **kwargs):
        kwargs.setdefault("exc_info", True)
        self.error(msg, *args, **kwargs)


class LoggerFactory:
    _instance = None
    _lock = threading.Lock()
    _loggers: Dict[str, ContextualLogger] = {}
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._setup_root_logger()
            self._initialized = True

    def _setup_root_logger(self):
        log_level = config.get("logging.level", "INFO")
        log_format = config.get("logging.format", "[%(asctime)s] [%(levelname)s] [%(name)s] [%(job_uuid)s] [%(trace_id)s] %(message)s")
        log_file_path = config.get("logging.file_path", "")
        max_file_size_mb = config.get("logging.max_file_size_mb", 10)
        backup_count = config.get("logging.backup_count", 5)

        # Create custom formatter
        formatter = ContextualFormatter(log_format)

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)

        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Add console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

        # Add file handler if configured
        if log_file_path:
            try:
                file_handler = RotatingFileHandler(
                    log_file_path,
                    maxBytes=max_file_size_mb * 1024 * 1024,
                    backupCount=backup_count,
                )
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)
            except Exception as e:
                console_handler.setLevel(logging.WARNING)
                root_logger.warning(f"Failed to setup file logging: {e}")

        # Configure Celery loggers
        self._configure_celery_loggers()

    def _configure_celery_loggers(self):
        celery_loggers = [
            "celery",
            "celery.worker",
            "celery.task",
            "celery.redirected",
            "celery.worker.strategy",
            "celery.worker.consumer",
            "celery.worker.heartbeat",
            "celery.worker.control",
            "celery.bootsteps",
            "kombu",
            "amqp",
        ]

        for logger_name in celery_loggers:
            celery_logger = logging.getLogger(logger_name)
            celery_logger.setLevel(logging.WARNING)
            celery_logger.propagate = False

    def get_logger(self, name: str) -> ContextualLogger:
        if name not in self._loggers:
            standard_logger = logging.getLogger(name)
            self._loggers[name] = ContextualLogger(standard_logger)

        return self._loggers[name]

    def set_context(self, job_uuid: Optional[str] = None, trace_id: Optional[str] = None, span_id: Optional[str] = None):
        if job_uuid is not None:
            job_uuid_context.set(job_uuid)
        if trace_id is not None:
            trace_id_context.set(trace_id)
        if span_id is not None:
            span_id_context.set(span_id)

    def clear_context(self):
        job_uuid_context.set(None)
        trace_id_context.set(None)
        span_id_context.set(None)


# Global factory instance
logger_factory = LoggerFactory()


def get_logger(name: str) -> ContextualLogger:
    return logger_factory.get_logger(name)


def set_logging_context(job_uuid: Optional[str] = None, trace_id: Optional[str] = None, span_id: Optional[str] = None):
    logger_factory.set_context(job_uuid, trace_id, span_id)


def clear_logging_context():
    logger_factory.clear_context()


class LoggingContext:
    def __init__(self, job_uuid: Optional[str] = None, trace_id: Optional[str] = None, span_id: Optional[str] = None):
        self.job_uuid = job_uuid
        self.trace_id = trace_id
        self.span_id = span_id
        self.previous_job_uuid = None
        self.previous_trace_id = None
        self.previous_span_id = None

    def __enter__(self):
        # Store previous context
        self.previous_job_uuid = job_uuid_context.get()
        self.previous_trace_id = trace_id_context.get()
        self.previous_span_id = span_id_context.get()

        # Set new context
        set_logging_context(self.job_uuid, self.trace_id, self.span_id)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore previous context
        if self.previous_job_uuid is not None:
            job_uuid_context.set(self.previous_job_uuid)
        else:
            job_uuid_context.set(None)

        if self.previous_trace_id is not None:
            trace_id_context.set(self.previous_trace_id)
        else:
            trace_id_context.set(None)

        if self.previous_span_id is not None:
            span_id_context.set(self.previous_span_id)
        else:
            span_id_context.set(None)
