from typing import Dict, List, Optional

from app.celery_app import celery_app
from app.config.logger_factory import get_logger

logger = get_logger(__name__)


@celery_app.task(name="gpu_memory.get_status")
def get_gpu_memory_status_task() -> Dict:
    try:
        from app.services.gpu_memory_manager import gpu_memory_manager

        memory_stats = gpu_memory_manager.get_memory_stats()

        # Add worker information
        import socket

        memory_stats["worker_hostname"] = socket.gethostname()
        memory_stats["worker_id"] = celery_app.current_task.request.id if celery_app.current_task else "unknown"

        logger.info(f"GPU memory status retrieved from worker {memory_stats['worker_hostname']}")
        return {"status": "success", "data": memory_stats}

    except Exception as e:
        logger.error(f"Failed to get GPU memory status: {str(e)}")
        return {"status": "error", "message": str(e), "worker_hostname": socket.gethostname() if "socket" in locals() else "unknown"}


@celery_app.task(name="gpu_memory.optimize")
def optimize_gpu_memory_task(device_ids: Optional[List[int]] = None) -> Dict:
    try:
        import socket

        from app.services.gpu_memory_manager import gpu_memory_manager

        worker_hostname = socket.gethostname()

        if not gpu_memory_manager.is_gpu_available():
            return {"status": "info", "message": f"GPU not available on worker {worker_hostname}", "worker_hostname": worker_hostname}

        # Get memory info before optimization
        memory_before = gpu_memory_manager.get_all_devices_memory_info()

        # Determine which devices to optimize
        if device_ids is None:
            device_ids = list(range(gpu_memory_manager._device_count))

        optimization_results = []

        for device_id in device_ids:
            try:
                # Get memory before optimization for this device
                mem_before = gpu_memory_manager.get_memory_info(device_id)

                # Optimize memory
                gpu_memory_manager.optimize_memory_usage(device_id)

                # Get memory after optimization
                mem_after = gpu_memory_manager.get_memory_info(device_id)

                freed_mb = mem_before["allocated"] - mem_after["allocated"]

                optimization_results.append({"device_id": device_id, "memory_before_mb": mem_before["allocated"], "memory_after_mb": mem_after["allocated"], "freed_mb": freed_mb, "success": True})

                logger.info(f"Optimized GPU {device_id}: freed {freed_mb:.2f} MB")

            except Exception as e:
                logger.error(f"Failed to optimize GPU {device_id}: {str(e)}")
                optimization_results.append({"device_id": device_id, "error": str(e), "success": False})

        # Get memory info after all optimizations
        memory_after = gpu_memory_manager.get_all_devices_memory_info()

        return {"status": "success", "message": f"GPU memory optimization completed on worker {worker_hostname}", "worker_hostname": worker_hostname, "optimization_results": optimization_results, "memory_before": memory_before, "memory_after": memory_after}

    except Exception as e:
        logger.error(f"Failed to optimize GPU memory: {str(e)}")
        return {"status": "error", "message": str(e), "worker_hostname": socket.gethostname() if "socket" in locals() else "unknown"}


@celery_app.task(name="gpu_memory.clear_cache")
def clear_gpu_cache_task(device_ids: Optional[List[int]] = None) -> Dict:
    try:
        import socket

        from app.services.gpu_memory_manager import gpu_memory_manager

        worker_hostname = socket.gethostname()

        if not gpu_memory_manager.is_gpu_available():
            return {"status": "info", "message": f"GPU not available on worker {worker_hostname}", "worker_hostname": worker_hostname}

        # Get memory info before clearing
        memory_before = gpu_memory_manager.get_all_devices_memory_info()

        # Clear cache
        if device_ids is None:
            gpu_memory_manager.clear_cache()
            logger.info(f"Cleared GPU cache for all devices on worker {worker_hostname}")
        else:
            for device_id in device_ids:
                gpu_memory_manager.clear_cache(device_id)
                logger.info(f"Cleared GPU cache for device {device_id} on worker {worker_hostname}")

        # Get memory info after clearing
        memory_after = gpu_memory_manager.get_all_devices_memory_info()

        return {"status": "success", "message": f"GPU cache cleared on worker {worker_hostname}", "worker_hostname": worker_hostname, "memory_before": memory_before, "memory_after": memory_after}

    except Exception as e:
        logger.error(f"Failed to clear GPU cache: {str(e)}")
        return {"status": "error", "message": str(e), "worker_hostname": socket.gethostname() if "socket" in locals() else "unknown"}


@celery_app.task(name="gpu_memory.get_all_workers_status")
def get_all_workers_gpu_status_task() -> Dict:
    try:
        import socket

        from celery import group

        api_hostname = socket.gethostname()

        # Get list of active workers
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()

        if not active_workers:
            return {"status": "warning", "message": "No active workers found", "api_hostname": api_hostname, "workers": []}

        # Create a group of tasks to get status from all workers
        worker_names = list(active_workers.keys())

        # Send get_gpu_memory_status_task to all workers
        job = group(get_gpu_memory_status_task.s() for _ in worker_names)
        result = job.apply_async()

        # Wait for all results (with timeout)
        worker_results = result.get(timeout=30)

        # Aggregate results
        workers_status = []
        total_gpus = 0
        total_memory_mb = 0
        total_allocated_mb = 0

        for i, worker_result in enumerate(worker_results):
            if worker_result["status"] == "success":
                worker_data = worker_result["data"]
                workers_status.append({"worker_hostname": worker_data.get("worker_hostname", f"worker_{i}"), "gpu_available": worker_data.get("gpu_available", False), "device_count": worker_data.get("device_count", 0), "current_memory": worker_data.get("current_memory", {}), "status": "success"})

                # Aggregate statistics
                if worker_data.get("gpu_available"):
                    total_gpus += worker_data.get("device_count", 0)
                    for device_memory in worker_data.get("current_memory", {}).values():
                        total_memory_mb += device_memory.get("total", 0)
                        total_allocated_mb += device_memory.get("allocated", 0)
            else:
                workers_status.append({"worker_hostname": worker_result.get("worker_hostname", f"worker_{i}"), "status": "error", "error": worker_result.get("message", "Unknown error")})

        return {"status": "success", "message": f"Retrieved GPU status from {len(workers_status)} workers", "api_hostname": api_hostname, "summary": {"total_workers": len(workers_status), "total_gpus": total_gpus, "total_memory_mb": total_memory_mb, "total_allocated_mb": total_allocated_mb, "utilization_percent": (total_allocated_mb / total_memory_mb * 100) if total_memory_mb > 0 else 0}, "workers": workers_status}

    except Exception as e:
        logger.error(f"Failed to get GPU status from all workers: {str(e)}")
        return {"status": "error", "message": str(e), "api_hostname": socket.gethostname() if "socket" in locals() else "unknown"}
