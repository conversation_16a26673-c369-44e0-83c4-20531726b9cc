exclude: ^(examples|tools|doc)
repos:
  - repo: **************:psf/black.git
    rev: 25.1.0
    hooks:
      - id: black
        language_version: python3
        args: ["--line-length=500"]

  - repo: **************:PyCQA/isort.git
    rev: 6.0.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--line-length", "500"]

  - repo: **************:PyCQA/flake8.git
    rev: 7.0.0
    hooks:
      - id: flake8
        args: ["--max-line-length=500"]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
    -   id: mypy

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v6.0.0
    hooks:
    - id: check-yaml
    - id: debug-statements
    - id: end-of-file-fixer
    - id: trailing-whitespace
  - repo: https://github.com/asottile/reorder-python-imports
    rev: v3.15.0
    hooks:
    - id: reorder-python-imports
      args: [--application-directories, '.:src', --py38-plus]
  - repo: https://github.com/psf/black-pre-commit-mirror
    rev: 25.1.0
    hooks:
    - id: black
      args: [--line-length=79, --target-version=py38]
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.20.0
    hooks:
    - id: pyupgrade
      args: [--py38-plus]
