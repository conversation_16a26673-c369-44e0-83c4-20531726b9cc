# CloudML v3 - Development Guidelines

## Build/Run Commands
- Install dependencies: `pip install -r requirements.txt`
- Run API server: `uvicorn app.main:app --host 0.0.0.0 --port 8000`
- Run worker: `rq worker --url redis://localhost:6379/0 objectdetection`
- Run with docker-compose: `docker-compose up`

## Lint/Format Commands
- Format code: `black .`
- Lint with ruff: `ruff check .`
- Run all checks: `trunk check`

## Test Commands
- Run all tests: `python -m pytest scripts/ -v`
- Run specific test file: `python -m pytest scripts/test_model_manager.py -v`
- Run single test: `python -m pytest scripts/test_model_manager.py::TestModelManager::test_singleton_pattern -v`

## Code Style Guidelines

### Imports
- Follow PEP 8 standards
- Group imports in order: standard library, third-party, local
- Use absolute imports when possible
- Avoid wildcard imports

### Formatting
- Line length: 500 characters (configured in pyproject.toml)
- Use Black formatter for consistent code style
- Ruff handles linting with B, D3, E, F rules (E501 ignored)

### Types
- Use type hints for function parameters and return values
- Leverage Pydantic models for data validation

### Naming Conventions
- Variables and functions: snake_case
- Classes: PascalCase
- Constants: UPPER_SNAKE_CASE
- Private members: prefixed with _

### Error Handling
- Use try/except blocks appropriately
- Log errors with contextual information
- Raise specific exceptions when possible
- Handle file operations with proper checks

## Project Structure
- app/: Main application code
- scripts/: Test files and utility scripts
- models are stored in app/ezlomodels/

## Testing
- Uses pytest framework
- Tests follow class-based structure with Test prefixed classes
- Use mocks for external dependencies
- Tests should be isolated and deterministic